-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.ref.dll"
-define:UNITY_2021_3_44
-define:UNITY_2021_3
-define:UNITY_2021
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_UNET
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_UNET
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:PLATFORM_STANDALONE
-define:TEXTCORE_1_0_OR_NEWER
-define:PLATFORM_STANDALONE_WIN
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:UNITY_UGP_API
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:ENABLE_WEBSOCKET_HOST
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_IG
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:INPUT_SYSTEM_1_4_OR_NEWER
-define:INPUT_SYSTEM_1_1_OR_NEWER
-define:ANIMATION_MODULE_PRESENT
-define:PHYSICS2D_MODULE_PRESENT
-define:XR_MANAGEMENT_4_0_OR_NEWER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIWidgetsModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"D:/Program Files/2021.3.44f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"D:/ProgramData/UnityProjectsD/VRasmb0722v0.4/Library/PackageCache/com.unity.collab-proxy@2.5.1/Lib/Editor/PlasticSCM/log4netPlastic.dll"
-r:"D:/ProgramData/UnityProjectsD/VRasmb0722v0.4/Library/PackageCache/com.unity.collab-proxy@2.5.1/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll"
-r:"D:/ProgramData/UnityProjectsD/VRasmb0722v0.4/Library/PackageCache/com.unity.collab-proxy@2.5.1/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll"
-r:"D:/ProgramData/UnityProjectsD/VRasmb0722v0.4/Library/PackageCache/com.unity.collab-proxy@2.5.1/Lib/Editor/PlasticSCM/unityplastic.dll"
-r:"D:/ProgramData/UnityProjectsD/VRasmb0722v0.4/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll"
-r:"D:/ProgramData/UnityProjectsD/VRasmb0722v0.4/Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"D:/ProgramData/UnityProjectsD/VRasmb0722v0.4/Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"D:/ProgramData/UnityProjectsD/VRasmb0722v0.4/Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"D:/ProgramData/UnityProjectsD/VRasmb0722v0.4/Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.ref.dll"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Jobs/ColorTweenJob.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Jobs/FloatTweenJob.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Jobs/ITweenJob.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Jobs/TweenJobData.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/Audio/AudioAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/BaseAffordanceStateReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/BaseAsyncAffordanceStateReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/BaseSynchronousAffordanceStateReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/IAffordanceStateReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/IAsyncAffordanceStateReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/ISynchronousAffordanceStateReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/Primitives/ColorAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/Primitives/FloatAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/Primitives/QuaternionAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/Primitives/QuaternionEulerAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/Primitives/Vector2AffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/Primitives/Vector3AffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/Primitives/Vector4AffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/Rendering/BlendShapeAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/Rendering/ColorGradientLineRendererAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/Rendering/ColorMaterialPropertyAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/Rendering/FloatMaterialPropertyAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/Rendering/Vector2MaterialPropertyAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/Rendering/Vector3MaterialPropertyAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/Rendering/Vector4MaterialPropertyAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/Transformation/UniformTransformScaleAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Receiver/UI/ImageColorAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Rendering/MaterialHelperBase.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Rendering/MaterialInstanceHelper.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Rendering/MaterialPropertyBlockHelper.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/State/Data/AffordanceStateData.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/State/Data/AffordanceStateShortcuts.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/State/Provider/BaseAffordanceStateProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/State/Provider/XRInteractableAffordanceStateProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/State/Provider/XRInteractorAffordanceStateProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Theme/Audio/AudioAffordanceThemeDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Theme/BaseAffordanceTheme.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Theme/Primitives/ColorAffordanceThemeDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Theme/Primitives/FloatAffordanceThemeDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Theme/Primitives/Vector2AffordanceThemeDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Theme/Primitives/Vector3AffordanceThemeDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AffordanceSystem/Theme/Primitives/Vector4AffordanceThemeDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/DragGesture.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/DragGesture.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/DragGestureRecognizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/Gesture.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/Gesture.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/GestureRecognizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/GestureRecognizer.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/GestureTouchesUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/PinchGesture.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/PinchGestureRecognizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/TapGesture.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/TapGestureRecognizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/TwistGesture.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/TwistGestureRecognizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/TwoFingerDragGesture.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/TwoFingerDragGesture.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Gestures/TwoFingerDragGestureRecognizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Interactables/ARAnnotationInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Interactables/ARBaseGestureInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Interactables/ARBaseGestureInteractable.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Interactables/ARPlacementInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Interactables/ARPlacementInteractable.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Interactables/ARRotationInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Interactables/ARScaleInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Interactables/ARSelectionInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Interactables/ARSelectionInteractable.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Interactables/ARTranslationInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Interactables/GestureTransformationUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Interactables/GestureTransformationUtility.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Interactors/ARGestureInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Interactors/ARGestureInteractor.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/Interactors/IARInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/TouchscreenGestureInputController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/TouchscreenGestureInputControllerState.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AR/TouchscreenGestureInputLayoutLoader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/AssemblyInfo.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/CardinalUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/Composites/FallbackComposite.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/InputActionManager.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/InputActionPropertyExtensions.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/Interactions/SectorInteraction.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/Simulation/Hands/HandExpressionCapture.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/Simulation/Hands/HandExpressionName.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/Simulation/Hands/XRDeviceSimulatorHandsProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/Simulation/Hands/XRDeviceSimulatorHandsSubsystem.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/Simulation/Hands/XRSimulatedHandState.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/Simulation/SimulatedInputLayoutLoader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/Simulation/XRDeviceSimulator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/Simulation/XRDeviceSimulatorLoader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/Simulation/XRDeviceSimulatorSettings.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/Simulation/XRSimulatedController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/Simulation/XRSimulatedControllerState.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/Simulation/XRSimulatedHMD.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/Simulation/XRSimulatedHMDState.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/XRHandSkeletonPokeDisplacer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/XRInputModalityManager.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/XRInputTrackingAggregator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Inputs/XRTransformStabilizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Attributes/CanFocusMultipleAttribute.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Attributes/CanSelectMultipleAttribute.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Attributes/XRTargetEvaluatorEnabledAttribute.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Controllers/ActionBasedController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Controllers/ActionBasedController.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Controllers/IXRScaleValueProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Controllers/XRBaseController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Controllers/XRBaseController.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Controllers/XRController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Controllers/XRControllerRecorder.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Controllers/XRControllerRecording.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Controllers/XRControllerRecording.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Controllers/XRControllerState.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Controllers/XRControllerState.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Controllers/XRScreenSpaceController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Controllers/XRScreenSpaceController.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Hover/IXRHoverFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/InteractionStrength/IXRInteractionStrengthFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/IXRFilterList.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Poke/IMultiPokeStateDataProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Poke/IPokeStateDataProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Poke/IXRPokeFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Poke/PokeStateData.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Poke/PokeThresholdData.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Poke/PokeThresholdDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Poke/PokeThresholdDatumProperty.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Poke/XRPokeFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Poke/XRPokeLogic.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Select/IXRSelectFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Target/Evaluators/IXRTargetEvaluatorLinkable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Target/Evaluators/XRAngleGazeEvaluator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Target/Evaluators/XRDistanceEvaluator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Target/Evaluators/XRLastSelectedEvaluator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Target/Evaluators/XRTargetEvaluator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Target/Filters/IXRTargetFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Target/Filters/XRBaseTargetFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Filtering/Target/Filters/XRTargetFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactables/DistanceInfo.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactables/IXRActivateInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactables/IXRFocusInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactables/IXRHoverInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactables/IXRInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactables/IXRInteractionStrengthInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactables/IXRSelectInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactables/Visual/XRTintInteractableVisual.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactables/XRBaseInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactables/XRBaseInteractable.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactables/XRGrabInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactables/XRGrabInteractable.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactables/XRInteractableSnapVolume.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactables/XRSimpleInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/IXRActivateInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/IXRGroupMember.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/IXRHoverInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/IXRInteractionGroup.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/IXRInteractionOverrideGroup.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/IXRInteractionStrengthInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/IXRInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/IXRRayProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/IXRSelectInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/IXRTargetPriorityInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/Visual/IXRCustomReticleProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/Visual/IXRReticleDirectionProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/Visual/XRInteractorLineVisual.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/Visual/XRInteractorReticleVisual.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/XRBaseControllerInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/XRBaseControllerInteractor.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/XRBaseInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/XRBaseInteractor.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/XRDirectInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/XRDirectInteractor.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/XRGazeInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/XRInteractionGroup.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/XRPokeInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/XRRayInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/XRRayInteractor.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/XRSocketInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Interactors/XRSocketInteractor.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Layers/InteractionLayerMask.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Layers/InteractionLayerSettings.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Overrides/IXRAimAssist.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Overrides/IXROverridesGazeAutoSelect.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Overrides/XRGazeAssistance.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Transformers/ARTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Transformers/IXRDropTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Transformers/IXRGrabTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Transformers/XRBaseGrabTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Transformers/XRDualGrabFreeTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Transformers/XRGeneralGrabTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Transformers/XRLegacyGrabTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Transformers/XRSingleGrabFreeTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/Transformers/XRSocketGrabTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/XRInteractionEvents.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/XRInteractionEvents.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/XRInteractionManager.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/XRInteractionManager.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Interaction/XRInteractionUpdateOrder.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/CharacterControllerDriver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/CharacterControllerDriver.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Climb/ClimbInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Climb/ClimbProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Climb/ClimbSettings.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Climb/ClimbSettingsDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Climb/ClimbSettingsDatumProperty.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/ConstrainedMoveProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Continuous/ActionBasedContinuousMoveProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Continuous/ActionBasedContinuousTurnProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Continuous/ContinuousMoveProviderBase.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Continuous/ContinuousTurnProviderBase.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Continuous/DeviceBasedContinuousMoveProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Continuous/DeviceBasedContinuousTurnProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Grab/GrabMoveProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Grab/TwoHandedGrabMoveProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/LocomotionProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/LocomotionProvider.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/LocomotionSystem.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/LocomotionSystem.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/SnapTurn/ActionBasedSnapTurnProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/SnapTurn/DeviceBasedSnapTurnProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/SnapTurn/SnapTurnProviderBase.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Teleportation/BaseTeleportationInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Teleportation/BaseTeleportationInteractable.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Teleportation/TeleportationAnchor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Teleportation/TeleportationArea.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/Teleportation/TeleportationProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Locomotion/TunnelingVignetteController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/BodyUI/FollowPresetDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/BodyUI/HandMenu.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/CanvasOptimizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/CanvasTracker.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/LazyFollow.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/MouseModel.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/NavigationModel.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/RegisteredUIInteractorCache.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/TouchModel.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/TrackedDeviceEventData.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/TrackedDeviceGraphicRaycaster.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/TrackedDeviceModel.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/TrackedDeviceModel.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/TrackedDevicePhysicsRaycaster.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/UIInputModule.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/UIInputModule.Events.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/XRUIInputModule.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/UI/XRUIInputModule.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/BurstGazeUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/BurstMathUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/BurstPhysicsUtils.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Collections/NativeCurve.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/ComponentLocatorUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Curves/CurveUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/DisposableManagerSingleton.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/EditorComponentLocatorUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/ExposedRegistrationList.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Interaction/XRFilterUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Interaction/XRInteractableUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Pooling/LinkedPool.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Pooling/PooledObject.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/ProjectPath.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/RegistrationList.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/RequireInterfaceAttribute.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/SmallRegistrationList.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/TeleportationMonitor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/TriggerContactMonitor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Tweenables/Primitives/ColorTweenableVariable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Tweenables/Primitives/FloatTweenableVariable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Tweenables/Primitives/QuaternionTweenableVariable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Tweenables/Primitives/Vector2TweenableVariable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Tweenables/Primitives/Vector3TweenableVariable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Tweenables/Primitives/Vector4TweenableVariable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Tweenables/SmartTweenableVariables/SmartFollowQuaternionTweenableVariable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Tweenables/SmartTweenableVariables/SmartFollowVector3TweenableVariable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Tweenables/TweenableVariableAsyncBase.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Tweenables/TweenableVariableBase.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/Utilities/Tweenables/TweenableVariableSynchronousBase.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/XR/GizmoHelpers.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/XR/InputHelpers.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/XR/SortingHelpers.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/XR/XRRig.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/XR/XRRig.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/XRHelpURLConstants.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@2.5.2/Runtime/XRHelpURLConstants.deprecated.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.AdditionalFile.txt"