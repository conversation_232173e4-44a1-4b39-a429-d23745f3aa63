# 子图切换优化说明

## 概述

针对您提出的嵌套父物体显示需求，我们对子图切换功能进行了全面优化，支持递归激活嵌套的父物体结构。

## 问题背景

### 原有问题
1. **控制方式不佳**：使用Renderer.enabled控制显示隐藏
2. **嵌套支持不足**：无法处理"左机械爪" → "左抓" → "零件"的嵌套结构
3. **性能问题**：隐藏的物体仍参与物理计算和交互

### 您的需求
- 切换到"左机械爪"子图时，需要显示：
  - 左机械爪下的直接零件
  - 左抓父物体下的所有零件
  - 支持任意深度的嵌套结构

## 解决方案

### 1. 改用Active控制

**优势对比**：

| 控制方式 | Active控制 | Renderer控制 |
|---------|-----------|-------------|
| 性能 | ✅ 完全禁用，不参与计算 | ❌ 仍参与物理计算 |
| 交互 | ✅ 避免意外交互 | ❌ 可能被意外点击 |
| 逻辑 | ✅ 简单清晰 | ❌ 需要处理多个组件 |
| 内存 | ✅ 更低内存占用 | ❌ 组件仍在内存中 |

**实现代码**：
```csharp
// 隐藏所有零件
private void HideAllParts()
{
    AssemblyPart[] allParts = FindObjectsOfType<AssemblyPart>();
    foreach (AssemblyPart part in allParts)
    {
        part.gameObject.SetActive(false); // 使用Active控制
    }
}
```

### 2. 递归嵌套激活

**核心功能**：
```csharp
// 递归激活所有子物体
private int ActivateChildrenRecursively(Transform parent)
{
    int count = 0;
    
    foreach (Transform child in parent)
    {
        // 激活子物体
        if (!child.gameObject.activeSelf)
        {
            child.gameObject.SetActive(true);
            count++;
        }
        
        // 递归处理嵌套的子物体
        count += ActivateChildrenRecursively(child);
    }
    
    return count;
}
```

**支持的层次结构示例**：
```
左机械爪 (父物体)
├── 零件A (直接子物体)
├── 零件B (直接子物体)
└── 左抓 (嵌套父物体)
    ├── 左抓零件1 (嵌套子物体)
    ├── 左抓零件2 (嵌套子物体)
    └── 更深层嵌套
        └── 深层零件
```

### 3. 增强的调试功能

**层次结构显示**：
```csharp
[ContextMenu("显示层次结构")]
public void DebugShowHierarchy(string parentName = "左机械爪")
{
    // 显示完整的层次结构和激活状态
}
```

**测试方法**：
```csharp
[ContextMenu("测试左机械爪嵌套激活")]
public void TestLeftClawNestedActivation()
{
    // 完整的测试流程
}
```

## 使用方式

### 1. 基本使用
```csharp
// 在HypergraphSequenceProcessor中
agentTools.SwitchSubgraph("subgraph_9"); // 切换到左机械爪
```

### 2. 子图映射配置
在`AgentTools.cs`的`GetParentNamesForSubgraph`方法中配置：
```csharp
case "subgraph_9": // 左机械爪子图
    return new string[] { "左机械爪" };
```

### 3. 调试和测试
1. **查看层次结构**：
   - 在Inspector中右键AgentTools组件
   - 选择"显示层次结构"

2. **测试嵌套激活**：
   - 右键选择"测试左机械爪嵌套激活"
   - 观察控制台日志

## 技术细节

### 激活流程
1. **隐藏所有零件**：`HideAllParts()`
2. **查找父物体**：`GameObject.Find(parentName)`
3. **递归激活**：`ActivateChildrenRecursively()`
4. **日志记录**：详细的激活信息

### 性能优化
- 使用`SetActive(false)`完全禁用GameObject
- 减少不必要的组件查找
- 批量操作减少调用次数

### 错误处理
- 父物体不存在的警告
- 未知子图ID的处理
- 详细的调试日志

## 配置指南

### 1. 添加新的子图映射
在`GetParentNamesForSubgraph`方法中添加：
```csharp
case "subgraph_新ID":
    return new string[] { "新父物体名称" };
```

### 2. 支持多个父物体
```csharp
case "subgraph_复合":
    return new string[] { "父物体1", "父物体2" };
```

### 3. 层次结构要求
- 确保父物体名称与场景中的GameObject名称完全匹配
- 子物体可以有任意深度的嵌套
- 建议使用有意义的命名规范

## 测试验证

### 测试步骤
1. **准备测试环境**：
   - 确保场景中有"左机械爪"父物体
   - 在"左机械爪"下创建"左抓"子父物体
   - 在各层级下放置测试零件

2. **执行测试**：
   ```csharp
   // 1. 隐藏所有零件
   agentTools.HideAllParts();
   
   // 2. 切换到左机械爪子图
   agentTools.SwitchSubgraph("subgraph_9");
   
   // 3. 验证结果
   agentTools.DebugShowHierarchy("左机械爪");
   ```

3. **验证结果**：
   - 左机械爪下的所有零件应该被激活
   - 包括嵌套在"左抓"下的零件
   - 其他子图的零件应该保持隐藏

### 预期日志输出
```
🙈 隐藏了 X 个零件（使用Active控制）
🔄 切换到子图: subgraph_9
👁️ 显示父物体: 左机械爪 及其子零件
  ✅ 激活子物体: 零件A
  ✅ 激活子物体: 左抓
  ✅ 激活子物体: 左抓零件1
  ✅ 激活子物体: 左抓零件2
👁️ 在父物体 左机械爪 下递归激活了 Y 个子物体
```

## 最佳实践

1. **命名规范**：
   - 使用清晰的父物体命名
   - 避免重复的GameObject名称

2. **层次结构设计**：
   - 合理组织嵌套层级
   - 避免过深的嵌套（建议不超过5层）

3. **性能考虑**：
   - 大量零件时考虑分批激活
   - 使用对象池管理复杂零件

4. **调试技巧**：
   - 经常使用层次结构显示功能
   - 观察控制台日志验证行为
   - 使用测试方法验证新配置

## 故障排除

### 问题：零件没有被激活
**可能原因**：
- 父物体名称不匹配
- 子图ID映射错误
- 零件不在指定父物体下

**解决方案**：
- 检查GameObject名称拼写
- 验证子图映射配置
- 使用DebugShowHierarchy查看结构

### 问题：嵌套零件没有显示
**可能原因**：
- 递归深度限制
- 中间父物体被禁用

**解决方案**：
- 检查中间层级的激活状态
- 确认递归逻辑正确执行

### 问题：性能问题
**解决方案**：
- 减少不必要的嵌套层级
- 考虑使用批量操作
- 优化零件数量
