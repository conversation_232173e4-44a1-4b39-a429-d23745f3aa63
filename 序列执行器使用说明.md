# 序列执行器使用说明

## 概述

项目中有两个序列执行器，它们都使用同一个Neo4jAssemblyController来执行装配动画：

1. **HypergraphSequenceProcessor** - 超图序列处理器（支持子图切换）
2. **SimpleSequenceExecutor** - 简单序列执行器（扁平图序列）

## 重要修改说明

### 问题背景
原本存在**三重空格键监听冲突**：
- HypergraphSequenceProcessor监听空格键
- SimpleSequenceExecutor监听空格键  
- Neo4jAssemblyController也监听空格键

这导致了动画时序混乱和不稳定的行为。

### 解决方案
两个序列执行器现在都会在启动时**自动禁用Neo4jAssemblyController的手动输入**，避免按键冲突。

## 使用方式

### 方式1：使用HypergraphSequenceProcessor（推荐）
适用于包含子图切换的复杂装配序列。

**特点**：
- ✅ 支持子图切换
- ✅ 支持装配步骤
- ✅ 支持工具调用
- ✅ 支持叙述步骤
- ✅ 完整的动画同步机制

**使用步骤**：
1. 在场景中添加HypergraphSequenceProcessor组件
2. 配置Assembly Controller引用
3. 设置序列文件路径
4. 运行场景，按空格键执行步骤

### 方式2：使用SimpleSequenceExecutor
适用于简单的扁平装配序列。

**特点**：
- ✅ 简单易用
- ✅ 支持装配步骤
- ✅ 动画同步机制
- ❌ 不支持子图切换
- ❌ 不支持工具调用

**使用步骤**：
1. 在场景中添加SimpleSequenceExecutor组件
2. 配置Assembly Controller引用
3. 设置序列数据文件（JSON格式）
4. 运行场景，按空格键执行步骤

## 重要注意事项

### ⚠️ 不要同时使用两个序列执行器
- 同一场景中只能激活一个序列执行器
- 两个执行器都会禁用Neo4jAssemblyController的手动输入
- 同时使用可能导致冲突

### ⚠️ 手动输入控制
- 当序列执行器运行时，Neo4jAssemblyController的手动输入被禁用
- 这意味着直接按空格键不会触发Neo4jAssemblyController的ExecuteNextStep
- 只有序列执行器可以控制装配步骤的执行

### ⚠️ 组件销毁时的清理
- 两个序列执行器都会在OnDestroy时重新启用Neo4jAssemblyController的手动输入
- 确保在切换场景或禁用组件时正确清理

## 技术实现细节

### 动画启动机制
由于Neo4jAssemblyController的`autoStartAnimation`默认为false，序列执行器需要：

1. 发送装配数据：`assemblyController.ReceiveExternalAssemblyData()`
2. 手动启动动画：`assemblyController.ExecuteNextStep()`
3. 等待动画完成：轮询检查`assemblyController.IsAnimating`

### 空格键冲突解决
```csharp
// 在序列执行器启动时
assemblyController.SetManualInputEnabled(false);

// 在序列执行器销毁时
assemblyController.SetManualInputEnabled(true);
```

### 动画同步机制
```csharp
// 等待动画完成
while (assemblyController.IsAnimating)
{
    yield return new WaitForSeconds(0.1f);
}
```

## 调试信息

### HypergraphSequenceProcessor日志
- `🔒 已禁用Neo4jAssemblyController的手动输入，避免按键冲突`
- `🚀 已手动启动装配动画`
- `⏳ 等待装配动画完成...`
- `✅ 装配动画完成`

### SimpleSequenceExecutor日志
- `🔒 已禁用Neo4jAssemblyController的手动输入，避免按键冲突`
- `🚀 已手动启动装配动画`
- `步骤 X 执行完成`

### Neo4jAssemblyController日志
- `手动输入已禁用`
- `手动输入已启用`

## 故障排除

### 问题：动画不执行
**可能原因**：
- Assembly Controller引用未设置
- autoStartAnimation为false但没有手动调用ExecuteNextStep

**解决方案**：
- 检查组件引用
- 确认序列执行器正确调用了ExecuteNextStep

### 问题：按空格键无响应
**可能原因**：
- Neo4jAssemblyController的手动输入被禁用
- 序列执行器正在等待动画完成

**解决方案**：
- 这是正常行为，等待当前动画完成
- 检查控制台是否有"等待动画"的日志

### 问题：多个序列执行器冲突
**解决方案**：
- 同一时间只激活一个序列执行器
- 禁用不需要的序列执行器组件

## 最佳实践

1. **选择合适的序列执行器**：
   - 复杂序列 → HypergraphSequenceProcessor
   - 简单序列 → SimpleSequenceExecutor

2. **正确配置引用**：
   - 确保Assembly Controller引用正确设置
   - 检查序列数据文件路径

3. **测试和调试**：
   - 启用调试日志查看执行流程
   - 观察UI状态显示
   - 检查控制台日志

4. **场景管理**：
   - 切换场景前确保组件正确清理
   - 避免在同一场景中使用多个序列执行器
