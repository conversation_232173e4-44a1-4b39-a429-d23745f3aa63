using UnityEngine;
using System.Collections;
using Unity.Mathematics;
using System;
using System.Collections.Generic;


public class AgentTools : MonoBehaviour
{
    // UI面板的预制件或单一面板（需在Inspector中赋值）
    public GameObject uiPanel;
    private Transform vrCamera; // VR相机，用于朝向计算

    void Start()
    {
        vrCamera = Camera.main.transform; // 假设主相机是VR相机
        if (uiPanel != null)
        {
            uiPanel.SetActive(false); // 初始隐藏
        }
    }

    // 1. 文本UI（使用激活/取消激活）
    public void ShowTextUI(string text)
    {
        if (uiPanel == null)
        {
            Debug.LogError("uiPanel is not assigned!");
            return;
        }

        TMPro.TextMeshProUGUI textComponent = uiPanel.GetComponentInChildren<TMPro.TextMeshProUGUI>();
        if (textComponent != null)
        {
            textComponent.text = text;
        }

        uiPanel.SetActive(true); // 激活面板
        uiPanel.transform.position = vrCamera.position + vrCamera.forward * 5f; // 更新位置

        //uiPanel.transform.LookAt(vrCamera); // 朝向用户
        uiPanel.transform.rotation = vrCamera.rotation;// 朝向用户

        StartCoroutine(HideUIPanelAfterDelay(5f)); // 5秒后自动隐藏
    }

    private IEnumerator HideUIPanelAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);
        if (uiPanel != null && uiPanel.activeSelf)
        {
            uiPanel.SetActive(false); // 取消激活
        }
    }

    // 2. 高亮
    public void HighlightPart(string targetPartName)
    {
        GameObject part = GameObject.Find(targetPartName); // 替换为你的零件查找逻辑
        if (part != null)
        {
            Renderer renderer = part.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material.color = Color.yellow; // 高亮颜色

                //RotateToUser(part); // 旋转模型朝向用户
                Debug.Log($"Highlighted {targetPartName} at {System.DateTime.Now}");
            }
        }
        else
        {
            Debug.LogWarning($"Part {targetPartName} not found");
        }
    }

    // 2.1 透明化零件
    public void MakePartsTransparent(string targetPartName, float radius = 5f)
    {
        GameObject part = GameObject.Find(targetPartName);
        if (part != null)
        {
            List<GameObject> objects = DetectNearbyObjects(part, radius);
            foreach (GameObject obj in objects)
            {
                MakePartTransparent(obj);
            }
            Debug.Log($"Made nearby parts transparent around {targetPartName} at {System.DateTime.Now}");
        }
        else
        {
            Debug.LogWarning($"Part {targetPartName} not found for transparency");
        }
    }

    // 2.2 高亮并透明化周围零件（组合方法）
    public void HighlightPartWithTransparency(string targetPartName, float radius = 5f)
    {
        // 先高亮目标零件
        HighlightPart(targetPartName);

        // 再透明化周围零件
        MakePartsTransparent(targetPartName, radius);

        Debug.Log($"Highlighted {targetPartName} and made nearby parts transparent at {System.DateTime.Now}");
    }

    // 2.3 恢复零件材质
    public void RestorePartsAroundTarget(string targetPartName, float radius = 5f)
    {
        GameObject part = GameObject.Find(targetPartName);
        if (part != null)
        {
            List<GameObject> objects = DetectNearbyObjects(part, radius);
            foreach (GameObject obj in objects)
            {
                RestorePartMaterial(obj);
            }
            Debug.Log($"Restored materials for parts around {targetPartName} at {System.DateTime.Now}");
        }
        else
        {
            Debug.LogWarning($"Part {targetPartName} not found for material restoration");
        }
    }

    private void RotateToUser(GameObject obj)
    {
        Quaternion quaternion = Quaternion.Euler(-90, 0, 0);
        Vector3 targetDirection = vrCamera.position - obj.transform.position;
        obj.transform.rotation = Quaternion.LookRotation(targetDirection);
        obj.transform.rotation *= quaternion;

    }

    // 3. 两零件连线
    public void ConnectParts(string part1Name, string part2Name)
    {
        GameObject part1 = GameObject.Find(part1Name);
        GameObject part2 = GameObject.Find(part2Name);
        if (part1 != null && part2 != null)
        {
            StartCoroutine(DrawLineAnimation(part1.transform, part2.transform));
        }
        else
        {
            Debug.LogWarning("One or both parts not found");
        }
    }

    private IEnumerator DrawLineAnimation(Transform part1, Transform part2)
    {
        LineRenderer lineRenderer = new GameObject("TempLine").AddComponent<LineRenderer>();
        lineRenderer.startWidth = 1f;
        lineRenderer.endWidth = 1f;
        lineRenderer.material = new Material(Shader.Find("Sprites/Default"));

        for (int i = 0; i < 3; i++) // 重复三次
        {
            lineRenderer.SetPosition(0, part1.position);
            lineRenderer.SetPosition(1, part2.position);
            // RotateToUser(part1.gameObject); // 旋转模型
            yield return new WaitForSeconds(0.5f); // 显示0.5秒
            lineRenderer.SetPosition(0, Vector3.zero);
            lineRenderer.SetPosition(1, Vector3.zero);
            yield return new WaitForSeconds(0.5f); // 隐藏0.5秒
        }
        Destroy(lineRenderer.gameObject);
    }

    // 4. 建立层次关系认识
    public void HighlightSubgraphSequence(string[] partSequence)
    {
        StartCoroutine(HighlightSequence(partSequence));
    }

    private IEnumerator HighlightSequence(string[] parts)
    {
        foreach (string partName in parts)
        {
            GameObject part = GameObject.Find(partName);
            if (part != null)
            {
                Renderer renderer = part.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material.color = Color.green;
                    yield return new WaitForSeconds(1f); // 每秒点亮一个
                    renderer.material.color = Color.white; // 恢复默认颜色
                }
            }
        }
    }

    // 5. 拆/装螺丝
    public void AssembleDisassembleScrew(Vector3 partPoint, string assemblyEdge, bool isAssemble)
    {
        Debug.Log($"{(isAssemble ? "Assembling" : "Disassembling")} screw at {partPoint} on edge {assemblyEdge} at {System.DateTime.Now}");
        // 假设已有动画函数，根据isAssemble调用不同动画
        // 示例：PlayAnimation(partPoint, assemblyEdge, isAssemble);
    }

    // 6. 引导用户检查零件
    public void GuideCheckParts(string[] partList)
    {
        StartCoroutine(DisplayPartCheck(partList));
    }

    // 7. 子图切换 - 隐藏/显示父物体下的零件
    public void SwitchSubgraph(string subgraphId)
    {
        Debug.Log($"🔄 切换到子图: {subgraphId}");

        // 首先隐藏所有零件
        HideAllParts();

        // 根据子图ID显示对应的父物体及其子零件
        ShowSubgraphParts(subgraphId);
    }

    /// <summary>
    /// 隐藏所有零件
    /// </summary>
    private void HideAllParts()
    {
        AssemblyPart[] allParts = FindObjectsOfType<AssemblyPart>();
        foreach (AssemblyPart part in allParts)
        {
            // 隐藏零件的渲染器
            Renderer[] renderers = part.GetComponentsInChildren<Renderer>();
            foreach (Renderer renderer in renderers)
            {
                renderer.enabled = false;
            }
        }

        Debug.Log($"🙈 隐藏了所有零件");
    }

    /// <summary>
    /// 根据子图ID显示对应的零件
    /// </summary>
    /// <param name="subgraphId">子图ID，如"subgraph_10"</param>
    private void ShowSubgraphParts(string subgraphId)
    {
        // 特殊处理：如果是subgraph_0，显示所有零件
        if (subgraphId == "subgraph_0")
        {
            ShowAllParts();
            return;
        }

        // 根据子图ID映射到对应的父物体名称
        string[] parentNames = GetParentNamesForSubgraph(subgraphId);

        if (parentNames == null || parentNames.Length == 0)
        {
            Debug.LogWarning($"⚠️ 未找到子图 {subgraphId} 对应的父物体");
            return;
        }

        foreach (string parentName in parentNames)
        {
            GameObject parentObject = GameObject.Find(parentName);
            if (parentObject != null)
            {
                // 显示父物体下的所有零件
                ShowParentAndChildren(parentObject);
                Debug.Log($"👁️ 显示父物体: {parentName} 及其子零件");
            }
            else
            {
                Debug.LogWarning($"⚠️ 未找到父物体: {parentName}");
            }
        }
    }

    /// <summary>
    /// 显示所有零件
    /// </summary>
    private void ShowAllParts()
    {
        AssemblyPart[] allParts = FindObjectsOfType<AssemblyPart>();
        foreach (AssemblyPart part in allParts)
        {
            Transform parttrans = part.transform;
            foreach (Transform child in parttrans)
            {
                child.gameObject.SetActive(true);
            }
        }

        Debug.Log($"👁️ 显示了所有零件");
    }

    /// <summary>
    /// 显示父物体及其所有子物体的渲染器
    /// </summary>
    /// <param name="parentObject">父物体</param>
    private void ShowParentAndChildren(GameObject parentObject)
    {
        // 显示父物体本身的渲染器
        Transform parobj = parentObject.transform;
        foreach (Transform child in parobj)
        {
            child.gameObject.SetActive(true);
        }

        //// 显示所有子物体中的AssemblyPart组件的渲染器
        //AssemblyPart[] childParts = parentObject.GetComponentsInChildren<AssemblyPart>();
        //foreach (AssemblyPart part in childParts)
        //{
        //    Renderer[] partRenderers = part.GetComponentsInChildren<Renderer>();
        //    foreach (Renderer renderer in partRenderers)
        //    {
        //        renderer.enabled = true;
        //    }
        //}
    }

    /// <summary>
    /// 根据子图ID获取对应的父物体名称
    /// 这个映射关系需要根据实际的场景层次结构来配置
    /// </summary>
    /// <param name="subgraphId">子图ID</param>
    /// <returns>父物体名称数组</returns>
    private string[] GetParentNamesForSubgraph(string subgraphId)
    {
        // 根据子图ID映射到父物体名称
        // 这个映射关系需要根据实际场景中的父物体层次结构来配置
        switch (subgraphId)
        {
            case "subgraph_10": // 左抓子图
                return new string[] { "左抓手" };

            case "subgraph_7": // 右抓子图
                return new string[] { "右抓手" };

            case "subgraph_9": // 左机械爪子图
                return new string[] { "左机械爪" };

            case "subgraph_6": // 右机械爪子图
                return new string[] { "右机械爪" };

            case "subgraph_8": // 主机架子图
                return new string[] { "主机架" };

            case "subgraph_5": // 手部整合子图
                return new string[] { "手部整体" };

            default:
                Debug.LogWarning($"⚠️ 未知的子图ID: {subgraphId}");
                return null;
        }
    }

    private IEnumerator DisplayPartCheck(string[] parts)
    {
        GameObject displayArea = new GameObject("PartDisplayArea");
        displayArea.transform.position = vrCamera.position + vrCamera.forward * 1.0f;
        displayArea.transform.rotation = Quaternion.LookRotation(vrCamera.forward);

        foreach (string partName in parts)
        {
            GameObject partPrefab = Resources.Load<GameObject>(partName); // 假设零件为Prefab
            if (partPrefab != null)
            {
                GameObject instance = Instantiate(partPrefab, displayArea.transform);
                instance.transform.localPosition = new Vector3(UnityEngine.Random.Range(-0.5f, 0.5f), 0, UnityEngine.Random.Range(-0.5f, 0.5f));
                StartCoroutine(RotatePart(instance));
            }
        }

        yield return new WaitForSeconds(10f); // 显示10秒
        Destroy(displayArea);
    }

    private IEnumerator RotatePart(GameObject part)
    {
        while (true)
        {
            part.transform.Rotate(0, 30 * Time.deltaTime, 0); // 缓慢旋转
            yield return null;
        }
    }
    /// <summary>
    /// 将单个零件设置为透明
    /// </summary>
    /// <param name="part">要透明化的零件GameObject</param>
    public void MakePartTransparent(GameObject part)
    {
        if (part == null)
        {
            Debug.LogWarning("⚠️ 尝试透明化空的GameObject");
            return;
        }

        // 获取零件的所有渲染器（包括子物体）
        Renderer[] renderers = part.GetComponentsInChildren<Renderer>();

        if (renderers.Length == 0)
        {
            Debug.LogWarning($"⚠️ 零件 {part.name} 没有找到渲染器组件");
            return;
        }

        // 从Resources/Materials文件夹加载透明材质
        Material transparentMaterial = Resources.Load<Material>("Materials/Transparent");

        if (transparentMaterial == null)
        {
            Debug.LogError("❌ 无法加载透明材质！请确保 Resources/Materials/Transparent.mat 文件存在");
            return;
        }

        // 应用透明材质到所有渲染器
        foreach (Renderer renderer in renderers)
        {
            if (renderer != null)
            {
                // 保存原始材质（如果需要恢复的话）
                if (!renderer.gameObject.GetComponent<OriginalMaterialStorage>())
                {
                    OriginalMaterialStorage storage = renderer.gameObject.AddComponent<OriginalMaterialStorage>();
                    storage.originalMaterials = renderer.materials;
                }

                // 应用透明材质
                Material[] newMaterials = new Material[renderer.materials.Length];
                for (int i = 0; i < newMaterials.Length; i++)
                {
                    newMaterials[i] = transparentMaterial;
                }
                renderer.materials = newMaterials;

                Debug.Log($"✅ 零件 {part.name} 的渲染器 {renderer.name} 已设置为透明");
            }
        }
    }

    /// <summary>
    /// 恢复零件的原始材质
    /// </summary>
    /// <param name="part">要恢复的零件GameObject</param>
    public void RestorePartMaterial(GameObject part)
    {
        if (part == null) return;

        Renderer[] renderers = part.GetComponentsInChildren<Renderer>();

        foreach (Renderer renderer in renderers)
        {
            if (renderer != null)
            {
                OriginalMaterialStorage storage = renderer.gameObject.GetComponent<OriginalMaterialStorage>();
                if (storage != null && storage.originalMaterials != null)
                {
                    renderer.materials = storage.originalMaterials;
                    DestroyImmediate(storage); // 清理存储组件
                    Debug.Log($"✅ 零件 {part.name} 的渲染器 {renderer.name} 材质已恢复");
                }
            }
        }
    }

    /// <summary>
    /// 用于存储原始材质的组件
    /// </summary>
    private class OriginalMaterialStorage : MonoBehaviour
    {
        public Material[] originalMaterials;
    }
      public static List<GameObject> DetectNearbyObjects(GameObject centerObject, float radius, LayerMask layerMask = default)
    {
        List<GameObject> nearbyObjects = new List<GameObject>();

        Vector3 center = centerObject.transform.position;
        Collider[] hitColliders;

        // 判断是否设置了 layerMask
        if (layerMask == default)
        {
            hitColliders = Physics.OverlapSphere(center, radius);
        }
        else
        {
            hitColliders = Physics.OverlapSphere(center, radius, layerMask);
        }

        foreach (Collider col in hitColliders)
        {
            // 排除自己
            if (col.gameObject != centerObject)
            {
                nearbyObjects.Add(col.gameObject);
            }
        }

        return nearbyObjects;
    }
}