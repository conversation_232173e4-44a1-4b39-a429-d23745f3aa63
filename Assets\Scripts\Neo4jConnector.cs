using UnityEngine;
using UnityEngine.Networking;
using SimpleJSON;
using System.Collections;
using System.Collections.Generic;
using System;

/// <summary>
/// Neo4j连接器类，用于在Unity中与Neo4j数据库交互
/// 简化版本，移除了可视化连接关系的功能
/// </summary>
public class Neo4jConnector : MonoBehaviour
{
    // Neo4j数据库连接配置
    [SerializeField] private string url = "http://localhost:7474/db/neo4j/tx/commit";
    [SerializeField] private string username = "neo4j";
    [SerializeField] private string password = "neo4j123456";

    /// <summary>
    /// 执行Neo4j查询并通过回调返回结果
    /// </summary>
    /// <param name="query">Cypher查询语句（JSON格式）</param>
    /// <param name="callback">处理结果的回调函数</param>
    /// <returns>协程</returns>
    public IEnumerator ExecuteQuery(string query, Action<string> callback)
    {
        Debug.Log($"[Neo4jConnector] 执行查询: {query}");

        using (UnityWebRequest request = new UnityWebRequest(url, "POST"))
        {
            byte[] bodyRaw = System.Text.Encoding.UTF8.GetBytes(query);
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/json");
            request.SetRequestHeader("Authorization", "Basic " + System.Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes(username + ":" + password)));

            yield return request.SendWebRequest();

            if (request.result == UnityWebRequest.Result.Success)
            {
                Debug.Log($"[Neo4jConnector] 查询成功，响应长度: {request.downloadHandler.text.Length}");
                callback?.Invoke(request.downloadHandler.text);
            }
            else
            {
                Debug.Log($"[Neo4jConnector] 查询失败: {request.error}");
                callback?.Invoke(null);
            }
        }
    }

    /// <summary>
    /// 获取Neo4j数据库URL
    /// </summary>
    public string DatabaseUrl => url;

    /// <summary>
    /// 获取Neo4j数据库用户名
    /// </summary>
    public string DatabaseUsername => username;

    /// <summary>
    /// 获取Neo4j数据库密码
    /// </summary>
    public string DatabasePassword => password;

    /// <summary>
    /// 测试数据库连接
    /// </summary>
    /// <returns>协程</returns>
    public IEnumerator TestConnection(Action<bool> callback)
    {
        string testQuery = "{\"statements\": [{\"statement\": \"RETURN 1 AS result\"}]}";

        bool connectionSuccess = false;

        yield return StartCoroutine(ExecuteQuery(testQuery, (result) => {
            connectionSuccess = !string.IsNullOrEmpty(result);
        }));

        Debug.Log($"[Neo4jConnector] 数据库连接测试: {(connectionSuccess ? "成功" : "失败")}");
        callback?.Invoke(connectionSuccess);
    }

    /// <summary>
    /// 查询指定部件的连接关系
    /// </summary>
    /// <param name="partName">部件名称</param>
    /// <param name="callback">处理结果的回调函数</param>
    /// <returns>协程</returns>
    public IEnumerator QueryConnections(string partName, Action<List<(string, string, string, string)>> callback)
    {
        string query = $"{{\"statements\": [{{\"statement\": \"MATCH (a {{name: '{partName}'}})-[r:CONNECTED]-(b) RETURN a.name, b.name, r.from_point, r.to_point\"}}]}}";

        List<(string, string, string, string)> connections = new List<(string, string, string, string)>();

        yield return StartCoroutine(ExecuteQuery(query, (jsonResponse) => {
            if (string.IsNullOrEmpty(jsonResponse)) return;

            JSONNode response = JSON.Parse(jsonResponse);
            if (response == null || response["results"].Count == 0)
            {
                Debug.LogError("[Neo4jConnector] JSON 解析失败或无结果！");
                return;
            }

            JSONNode dataArray = response["results"][0]["data"];
            if (dataArray == null || dataArray.Count == 0)
            {
                Debug.Log($"[Neo4jConnector] 部件 {partName} 没有连接！");
                return;
            }

            Debug.Log($"[Neo4jConnector] 部件 {partName} 查询到 {dataArray.Count} 个连接");

            foreach (JSONNode row in dataArray.AsArray)
            {
                string partA = row["row"][0];
                string partB = row["row"][1];
                string fromPoint = row["row"][2];
                string toPoint = row["row"][3];

                connections.Add((partA, partB, fromPoint, toPoint));
                Debug.Log($"[Neo4jConnector] 发现连接: {partA}[{fromPoint}] <-> {partB}[{toPoint}]");
            }
        }));

        callback?.Invoke(connections);
    }
}