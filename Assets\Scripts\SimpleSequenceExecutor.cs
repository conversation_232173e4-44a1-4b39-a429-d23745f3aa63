using UnityEngine;
using System.Collections.Generic;
using SimpleJSON;

/// <summary>
/// 简单序列执行器
/// 按空格键逐步执行装配序列
/// </summary>
public class SimpleSequenceExecutor : MonoBehaviour
{
    [Header("组件引用")]
    [SerializeField] private Neo4jAssemblyController assemblyController;
    
    [Header("序列数据")]
    [SerializeField] private TextAsset sequenceDataFile; // 任务1扁平图序列1.json
    
    [Header("执行设置")]
    [SerializeField] private KeyCode executeKey = KeyCode.Space; // 执行按键
    [SerializeField] private KeyCode resetKey = KeyCode.R; // 重置按键
    
    // 内部状态
    private List<SequenceStep> sequenceSteps = new List<SequenceStep>();
    private int currentStepIndex = 0;
    private bool isExecuting = false;
    
    /// <summary>
    /// 序列步骤数据结构
    /// </summary>
    [System.Serializable]
    public struct SequenceStep
    {
        public string movingPartName;
        public string targetPartName;
        public string movingPartRefPoint;
        public string targetPartRefPoint;
        public string connectionType;
        public string fastenerType;
        public string screwType;
        public string nutType;
        public List<string> additionalMountPoints;
    }
    
    void Start()
    {
        // 自动查找装配控制器
        if (assemblyController == null)
            assemblyController = FindObjectOfType<Neo4jAssemblyController>();
            
        if (assemblyController == null)
        {
            Debug.LogError("[SimpleSequenceExecutor] 未找到Neo4jAssemblyController组件！");
            enabled = false;
            return;
        }
        
        // 加载序列数据
        LoadSequenceData();

        // 禁用Neo4jAssemblyController的手动输入，避免空格键冲突
        assemblyController.SetManualInputEnabled(false);
        Debug.Log("[SimpleSequenceExecutor] 🔒 已禁用Neo4jAssemblyController的手动输入，避免按键冲突");

        Debug.Log($"[SimpleSequenceExecutor] 初始化完成，共{sequenceSteps.Count}个步骤");
        Debug.Log($"按 {executeKey} 键执行下一步，按 {resetKey} 键重置");
    }

    void OnDestroy()
    {
        // 重新启用Neo4jAssemblyController的手动输入
        if (assemblyController != null)
        {
            assemblyController.SetManualInputEnabled(true);
            Debug.Log("[SimpleSequenceExecutor] 🔓 已重新启用Neo4jAssemblyController的手动输入");
        }
    }

    void Update()
    {
        // 检测按键
        if (Input.GetKeyDown(executeKey))
        {
            Debug.Log($"2");
            ExecuteNextStep();
        }
        else if (Input.GetKeyDown(resetKey))
        {
            ResetSequence();
        }
    }
    
    /// <summary>
    /// 加载序列数据
    /// </summary>
    private void LoadSequenceData()
    {
        if (sequenceDataFile == null)
        {
            Debug.LogError("[SimpleSequenceExecutor] 序列数据文件未配置！");
            return;
        }
        
        try
        {
            sequenceSteps.Clear();
            
            var jsonObject = JSON.Parse(sequenceDataFile.text);
            var sequenceArray = jsonObject["sequence"];
            
            foreach (JSONNode stepNode in sequenceArray.AsArray)
            {
                var step = new SequenceStep
                {
                    movingPartName = stepNode["movingPartName"],
                    targetPartName = stepNode["targetPartName"],
                    movingPartRefPoint = stepNode["movingPartRefPoint"],
                    targetPartRefPoint = stepNode["targetPartRefPoint"],
                    connectionType = stepNode["connectionType"],
                    fastenerType = stepNode["fastenerType"],
                    screwType = stepNode["screwType"],
                    nutType = stepNode["nutType"],
                    additionalMountPoints = new List<string>()
                };
                
                // 解析额外装配点
                if (stepNode["additionalMountPoints"] != null)
                {
                    foreach (JSONNode mountPoint in stepNode["additionalMountPoints"].AsArray)
                    {
                        step.additionalMountPoints.Add(mountPoint.Value);
                    }
                }
                
                sequenceSteps.Add(step);
            }
            
            Debug.Log($"[SimpleSequenceExecutor] 成功加载{sequenceSteps.Count}个装配步骤");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[SimpleSequenceExecutor] 加载序列数据失败: {e.Message}");
        }
    }
    
    /// <summary>
    /// 执行下一步
    /// </summary>
    public void ExecuteNextStep()
    {
        if (isExecuting)
        {
            Debug.LogWarning("[SimpleSequenceExecutor] 正在执行中，请等待完成");
            return;
        }
        
        if (currentStepIndex >= sequenceSteps.Count)
        {
            Debug.Log("[SimpleSequenceExecutor] 所有步骤已执行完成！");
            return;
        }
        
        var currentStep = sequenceSteps[currentStepIndex];
        Debug.Log($"[SimpleSequenceExecutor] 执行步骤 {currentStepIndex + 1}/{sequenceSteps.Count}: {currentStep.movingPartName} -> {currentStep.targetPartName}");
        
        // 转换当前步骤为系统内部格式
        string stepData = ConvertStepToInternalFormat(currentStep);
        
        // 发送给装配控制器
        assemblyController.ReceiveExternalAssemblyData($"Step_{currentStepIndex + 1}", stepData);

        // 手动启动装配动画（因为autoStartAnimation被禁用）
        assemblyController.ExecuteNextStep();
        Debug.Log("[SimpleSequenceExecutor] 🚀 已手动启动装配动画");

        currentStepIndex++;
        isExecuting = true;
        
        // 监听装配完成事件（如果有的话）
        StartCoroutine(WaitForStepCompletion());
    }
    
    /// <summary>
    /// 等待步骤完成
    /// </summary>
    private System.Collections.IEnumerator WaitForStepCompletion()
    {
        // 等待一小段时间让动画开始
        yield return new UnityEngine.WaitForSeconds(0.1f);

        // 等待装配动画真正完成
        while (assemblyController.IsAnimating)
        {
            yield return new UnityEngine.WaitForSeconds(0.1f); // 每0.1秒检查一次
        }

        isExecuting = false;
        Debug.Log($"[SimpleSequenceExecutor] 步骤 {currentStepIndex} 执行完成");
        
        if (currentStepIndex < sequenceSteps.Count)
        {
            Debug.Log($"[SimpleSequenceExecutor] 按 {executeKey} 键执行下一步 ({currentStepIndex + 1}/{sequenceSteps.Count})");
        }
        else
        {
            Debug.Log("[SimpleSequenceExecutor] 🎉 所有装配步骤已完成！");
        }
    }
    
    /// <summary>
    /// 转换单个步骤为系统内部格式
    /// </summary>
    private string ConvertStepToInternalFormat(SequenceStep step)
    {
        var stepsArray = new JSONArray();
        var stepObj = new JSONObject();
        
        stepObj["movingPart"] = step.movingPartName;
        stepObj["targetPart"] = step.targetPartName;
        stepObj["movingRefPoint"] = step.movingPartRefPoint;
        stepObj["targetRefPoint"] = step.targetPartRefPoint;
        stepObj["connectionType"] = step.connectionType;
        
        // 紧固件信息
        var fastener = new JSONObject();
        fastener["type"] = step.fastenerType;
        fastener["screwType"] = step.screwType;
        fastener["nutType"] = step.nutType;
        stepObj["fastener"] = fastener;
        
        // 额外装配点
        var additionalMountPoints = new JSONArray();
        foreach (string mountPointStr in step.additionalMountPoints)
        {
            // 解析格式: "主机架反_P5,安装连接块反2_P0,SCREW,SCREW_NUT,M3X10-10,Nut"
            string[] parts = mountPointStr.Split(',');
            if (parts.Length >= 6)
            {
                var mountPoint = new JSONObject();
                mountPoint["partName"] = parts[0];
                mountPoint["mountPoint"] = parts[1];
                mountPoint["screwType"] = parts[4];
                mountPoint["nutType"] = parts[5];
                additionalMountPoints.Add(mountPoint);
            }
        }
        stepObj["additionalMountPoints"] = additionalMountPoints;
        
        stepsArray.Add(stepObj);
        
        var result = new JSONObject();
        result["steps"] = stepsArray;
        
        return result.ToString();
    }
    
    /// <summary>
    /// 重置序列
    /// </summary>
    public void ResetSequence()
    {
        Debug.Log("[SimpleSequenceExecutor] 重置装配序列");
        
        currentStepIndex = 0;
        isExecuting = false;
        
        // 重置装配控制器
        if (assemblyController != null)
        {
            assemblyController.ResetAssembly();
        }
        
        Debug.Log($"[SimpleSequenceExecutor] 序列已重置，按 {executeKey} 键开始执行第一步");
    }
    
    /// <summary>
    /// 获取当前状态信息
    /// </summary>
    public string GetStatusInfo()
    {
        if (sequenceSteps.Count == 0)
            return "无序列数据";
            
        if (currentStepIndex >= sequenceSteps.Count)
            return "序列已完成";
            
        return $"步骤 {currentStepIndex + 1}/{sequenceSteps.Count}";
    }
    
    // 在Inspector中显示当前状态
    void OnGUI()
    {
        if (!Application.isPlaying) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 300, 120));
        GUILayout.Label($"装配序列状态: {GetStatusInfo()}");
        GUILayout.Label($"按 {executeKey} 键执行下一步");
        GUILayout.Label($"按 {resetKey} 键重置序列");
        GUILayout.Label("🔒 Neo4j手动输入已禁用（避免按键冲突）");
        if (isExecuting)
        {
            GUILayout.Label("正在执行中...");
        }
        GUILayout.EndArea();
    }
    
    // 测试方法
    [ContextMenu("执行下一步")]
    public void TestExecuteNextStep()
    {
        ExecuteNextStep();
    }
    
    [ContextMenu("重置序列")]
    public void TestResetSequence()
    {
        ResetSequence();
    }
}
