Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.44f1c1 (0c301d0bd4e3) revision 798749'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 16091 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.44f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/nwu/Assembly/VRasmb0722v0.4
-logFile
Logs/AssetImportWorker1.log
-srvPort
6806
Successfully changed project path to: D:/nwu/Assembly/VRasmb0722v0.4
D:/nwu/Assembly/VRasmb0722v0.4
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [41172] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1186022802 [EditorId] 1186022802 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [41172] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1186022802 [EditorId] 1186022802 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 71.23 ms, found 18 plugins.
Preloading 2 native plugins for Editor in 3.12 ms.
Initialize engine version: 2021.3.44f1c1 (0c301d0bd4e3)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/nwu/Assembly/VRasmb0722v0.4/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 Laptop GPU (ID=0x2860)
    Vendor:   NVIDIA
    VRAM:     7948 MB
    Driver:   32.0.15.6624
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56172
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.017156 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1260 ms
Refreshing native plugins compatible for Editor in 37.88 ms, found 18 plugins.
Preloading 2 native plugins for Editor in 0.20 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.869 seconds
Domain Reload Profiling:
	ReloadAssembly (1870ms)
		BeginReloadAssembly (147ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (1601ms)
			LoadAssemblies (138ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (98ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (16ms)
			SetupLoadedEditorAssemblies (1443ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (1322ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (38ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (56ms)
				ProcessInitializeOnLoadMethodAttributes (26ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.014315 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 40.96 ms, found 18 plugins.
Preloading 2 native plugins for Editor in 0.23 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff7213befad (Unity) StackWalker::GetCurrentCallstack
0x00007ff7213c5b39 (Unity) StackWalker::ShowCallstack
0x00007ff722368df3 (Unity) GetStacktrace
0x00007ff722a3165d (Unity) DebugStringToFile
0x00007ff72050ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000242ccb2e553 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000242ccb2e22b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000242ccb2dfb0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000242ccb2de78 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x00000242ccb2af43 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x00000242ccbaa9f5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb0e6f697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb0e62cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb0e627b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb0e55ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb0e58febc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x00000242ccbaa09a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x00000242ccba9fab (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x00000242ccba96f3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x00000242cd2e6298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb0e6f697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb0e62cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb0e62cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff7212e1934 (Unity) scripting_method_invoke
0x00007ff7212c0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff7212bb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff7213ff4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff7212b570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff7212ab1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff7212b2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff72221fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff722220144 (Unity) LoadUserAssemblies
0x00007ff721f20c43 (Unity) Application::InitializeProject
0x00007ff7223720a0 (Unity) WinMain
0x00007ff72373fbae (Unity) __scrt_common_main_seh
0x00007ffb90be259d (KERNEL32) BaseThreadInitThunk
0x00007ffb9198af78 (ntdll) RtlUserThreadStart

Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.484 seconds
Domain Reload Profiling:
	ReloadAssembly (1484ms)
		BeginReloadAssembly (117ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (12ms)
		EndReloadAssembly (1261ms)
			LoadAssemblies (144ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (189ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (39ms)
			SetupLoadedEditorAssemblies (878ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (41ms)
				BeforeProcessingInitializeOnLoad (69ms)
				ProcessInitializeOnLoadAttributes (708ms)
				ProcessInitializeOnLoadMethodAttributes (20ms)
				AfterProcessingInitializeOnLoad (25ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.16 seconds
Refreshing native plugins compatible for Editor in 0.70 ms, found 18 plugins.
Preloading 2 native plugins for Editor in 0.17 ms.
Unloading 4788 Unused Serialized files (Serialized files now loaded: 0)
Unloading 71 unused Assets / (122.9 KB). Loaded Objects now: 5240.
Memory consumption went from 195.4 MB to 195.3 MB.
Total: 2.514300 ms (FindLiveObjects: 0.214600 ms CreateObjectMapping: 0.054700 ms MarkObjects: 2.172800 ms  DeleteObjects: 0.071300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
