Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.44f1c1 (0c301d0bd4e3) revision 798749'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 16091 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.44f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/nwu/Assembly/VRasmb0722v0.4
-logFile
Logs/AssetImportWorker0.log
-srvPort
6806
Successfully changed project path to: D:/nwu/Assembly/VRasmb0722v0.4
D:/nwu/Assembly/VRasmb0722v0.4
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [14988] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3816198281 [EditorId] 3816198281 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [14988] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3816198281 [EditorId] 3816198281 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 73.66 ms, found 18 plugins.
Preloading 2 native plugins for Editor in 2.04 ms.
Initialize engine version: 2021.3.44f1c1 (0c301d0bd4e3)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/nwu/Assembly/VRasmb0722v0.4/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 Laptop GPU (ID=0x2860)
    Vendor:   NVIDIA
    VRAM:     7948 MB
    Driver:   32.0.15.6624
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56036
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.017661 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1385 ms
Refreshing native plugins compatible for Editor in 39.35 ms, found 18 plugins.
Preloading 2 native plugins for Editor in 0.22 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.011 seconds
Domain Reload Profiling:
	ReloadAssembly (2011ms)
		BeginReloadAssembly (150ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (1734ms)
			LoadAssemblies (144ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (106ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (15ms)
			SetupLoadedEditorAssemblies (1564ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (1442ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (40ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (56ms)
				ProcessInitializeOnLoadMethodAttributes (25ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.012502 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 39.97 ms, found 18 plugins.
Preloading 2 native plugins for Editor in 0.22 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff7213befad (Unity) StackWalker::GetCurrentCallstack
0x00007ff7213c5b39 (Unity) StackWalker::ShowCallstack
0x00007ff722368df3 (Unity) GetStacktrace
0x00007ff722a3165d (Unity) DebugStringToFile
0x00007ff72050ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000024e469be553 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x0000024e469be22b (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x0000024e469bdfb0 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x0000024e469bde78 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x0000024e469baf43 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x0000024e46a2a9f5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb0e6f697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb0e62cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb0e627b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb0e55ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb0e58febc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x0000024e46a2a09a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000024e46a29fab (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000024e46a296f3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x0000024e47166298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb0e6f697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb0e62cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb0e62cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff7212e1934 (Unity) scripting_method_invoke
0x00007ff7212c0b84 (Unity) ScriptingInvocation::Invoke
0x00007ff7212bb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff7213ff4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff7212b570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff7212ab1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff7212b2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff72221fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff722220144 (Unity) LoadUserAssemblies
0x00007ff721f20c43 (Unity) Application::InitializeProject
0x00007ff7223720a0 (Unity) WinMain
0x00007ff72373fbae (Unity) __scrt_common_main_seh
0x00007ffb90be259d (KERNEL32) BaseThreadInitThunk
0x00007ffb9198af78 (ntdll) RtlUserThreadStart

Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.457 seconds
Domain Reload Profiling:
	ReloadAssembly (1457ms)
		BeginReloadAssembly (118ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (11ms)
		EndReloadAssembly (1244ms)
			LoadAssemblies (111ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (203ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (40ms)
			SetupLoadedEditorAssemblies (875ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (40ms)
				BeforeProcessingInitializeOnLoad (68ms)
				ProcessInitializeOnLoadAttributes (707ms)
				ProcessInitializeOnLoadMethodAttributes (20ms)
				AfterProcessingInitializeOnLoad (26ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.18 seconds
Refreshing native plugins compatible for Editor in 0.70 ms, found 18 plugins.
Preloading 2 native plugins for Editor in 0.18 ms.
Unloading 4788 Unused Serialized files (Serialized files now loaded: 0)
Unloading 71 unused Assets / (122.3 KB). Loaded Objects now: 5240.
Memory consumption went from 195.2 MB to 195.1 MB.
Total: 2.683600 ms (FindLiveObjects: 0.184400 ms CreateObjectMapping: 0.055100 ms MarkObjects: 2.281500 ms  DeleteObjects: 0.161400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 336897.147494 seconds.
  path: Assets/Scenes/task1_hyper.unity
  artifactKey: Guid(b8324aa0d0d492443909f72e183cc547) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/task1_hyper.unity using Guid(b8324aa0d0d492443909f72e183cc547) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '04017093e67d6791256d4d57143221a3') in 0.006152 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 20.842253 seconds.
  path: Assets/Scripts/Scripts.zip
  artifactKey: Guid(4142581a606c3024fb2f967e10df5a00) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scripts/Scripts.zip using Guid(4142581a606c3024fb2f967e10df5a00) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7167b44c0a4236b985bd1786b70fc8cf') in 0.023794 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 29.383637 seconds.
  path: Assets/Scripts/HypergraphSequenceProcessor.cs
  artifactKey: Guid(2abdf804382f7de42be6c1702eddc768) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scripts/HypergraphSequenceProcessor.cs using Guid(2abdf804382f7de42be6c1702eddc768) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ae7669874b6eada4ac479ad2ea35de5b') in 0.008441 seconds 
Number of asset objects unloaded after import = 0
